#!/usr/bin/env python3
"""
双角度估计主程序
"""

import numpy as np
import torch
import os

from models import HH2ComplexMLPDual, HH2ComplexCONVDual
from data_preprocessor import preprocess_quadriga_data_dual
from trainer import QuadrigaTrainerDual
from evaluator import evaluate_dual_model, print_dual_results
from visualizer import plot_dual_results


def main_dual():
    """支持双角度估计的主函数"""
    print("🚀 Quadriga双角度估计训练开始")
    print("="*80)

    # 配置参数
    config = {
        'data_path': '/home/<USER>/quadriga/uma_v0/',
        'model_type': 'MLP',  # 'MLP', 'CNN'
        'output_type': 'dual',  # 'dual', 'azimuth', 'elevation'
        'batch_size': 32,
        'num_epochs': 100,
        'learning_rate': 0.0001,
        'patience': 20,
        'test_split': 0.2,
        'normalize': True,
        'use_covariance': False,
        'loss_weights': None,  # [azimuth_weight, elevation_weight]
        'save_dir': './results/',
        'model_save_path': './best_dual_model.pth',
        'bs_location': [0, 0, 10]  # 基站位置 [x, y, z]
    }

    # 打印配置
    print("⚙️  训练配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    print()

    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️  使用设备: {device}")
    print()

    try:
        # 数据预处理
        train_loader, val_loader, data_info = preprocess_quadriga_data_dual(
            data_path=config['data_path'],
            model_type=config['model_type'],
            output_type=config['output_type'],
            use_covariance=config['use_covariance'],
            normalize=config['normalize'],
            test_split=config['test_split'],
            batch_size=config['batch_size'],
            bs_location=np.array(config['bs_location'])
        )

        # 模型初始化
        print(f"\n🏗️  初始化{config['model_type']}双角度模型...")

        if config['model_type'] == 'MLP':
            model = HH2ComplexMLPDual(
                input_size=data_info['input_size'],
                device=device,
                output_type=config['output_type']
            )
        elif config['model_type'] == 'CNN':
            model = HH2ComplexCONVDual(
                input_size=data_info['input_size'], 
                device=device,
                output_type=config['output_type']
            )
        else:
            raise ValueError(f"不支持的模型类型: {config['model_type']}")

        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

        # 训练器初始化
        trainer = QuadrigaTrainerDual(
            model, device, 
            output_type=config['output_type'],
            loss_weights=config['loss_weights']
        )

        # 开始训练
        print(f"\n🎯 开始训练...")
        best_mae = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=config['num_epochs'],
            learning_rate=config['learning_rate'],
            patience=config['patience'],
            save_path=config['model_save_path']
        )

        print(f"\n✅ 训练完成! 最佳Combined MAE: {best_mae:.6f}")

        # 最终评估
        print(f"\n🔍 进行最终双角度评估...")
        metrics = evaluate_dual_model(
            model=model,
            test_loader=val_loader,
            device=device,
            output_type=config['output_type'],
            model_save_path=config['model_save_path']
        )

        # 打印结果
        print_dual_results(metrics, config['output_type'])

        # 绘制结果
        print(f"\n📊 生成双角度结果图表...")
        plot_dual_results(trainer, metrics, config['output_type'], config['save_dir'])

        print(f"\n🎉 双角度估计任务完成!")
        return metrics

    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    try:
        final_metrics = main_dual()
        if final_metrics:
            print(f"\n🏆 最终Combined MAE: {final_metrics['combined_mae']:.6f}°")
    except KeyboardInterrupt:
        print(f"\n⚠️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
